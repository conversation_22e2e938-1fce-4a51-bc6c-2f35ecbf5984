defmodule ReconciliationWeb.Admin.UsersLive do
  use ReconciliationWeb, :live_view

  alias Reconciliation.UserManagement
  alias Reconciliation.Services.ActivityLogger

  @impl true
  def mount(_params, _session, socket) do
    IO.inspect(socket.assigns.current_user, label: "DEBUG: Current user in mount")

    # Check if user has permission to manage users
    can_manage = can_manage_users?(socket.assigns.current_user)
    IO.inspect(can_manage, label: "DEBUG: Can manage users?")

    if can_manage do
      users = UserManagement.list_users()

      socket =
        socket
        |> assign(:page_title, "User Management")
        |> assign(:users, users)
        |> assign(:search_term, "")
        |> assign(:status_filter, "all")
        |> assign(:organization_filter, "all")
        |> assign(:pending_status_changes, %{})

      {:ok, socket}
    else
      IO.inspect("DEBUG: User does not have permission, redirecting to dashboard")
      socket =
        socket
        |> put_flash(:error, "You don't have permission to access this page.")
        |> redirect(to: ~p"/dashboard")

      {:ok, socket}
    end
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "User Management")
    |> assign(:show_form, false)
    |> assign(:form_mode, nil)
  end

  defp apply_action(socket, :show, %{"id" => id}) do
    user = UserManagement.get_user!(id)
    socket
    |> assign(:page_title, "User Details")
    |> assign(:selected_user, user)
  end

  defp apply_action(socket, :new, _params) do
    changeset = UserManagement.change_user(%{})
    organizations = UserManagement.list_organizations()
    roles = UserManagement.list_roles()

    socket
    |> assign(:page_title, "Create New User")
    |> assign(:user_form, to_form(changeset))
    |> assign(:organizations, organizations)
    |> assign(:roles, roles)
    |> assign(:show_form, true)
    |> assign(:form_mode, :new)
  end

  defp apply_action(socket, :edit, %{"id" => id}) do
    user = UserManagement.get_user!(id)
    changeset = UserManagement.change_user_for_edit(user)
    organizations = UserManagement.list_organizations()
    roles = UserManagement.list_roles()

    socket
    |> assign(:page_title, "Edit User")
    |> assign(:user, user)
    |> assign(:user_form, to_form(changeset))
    |> assign(:organizations, organizations)
    |> assign(:roles, roles)
    |> assign(:show_form, true)
    |> assign(:form_mode, :edit)
  end

  defp apply_action(socket, :show, %{"id" => id}) do
    user = UserManagement.get_user!(id)

    socket
    |> assign(:page_title, "User Details")
    |> assign(:user, user)
    |> assign(:show_form, true)
    |> assign(:form_mode, :show)
  end

  @impl true
  def handle_event("search", %{"search" => search_term}, socket) do
    users = UserManagement.list_users(search: search_term)
    
    socket = 
      socket
      |> assign(:users, users)
      |> assign(:search_term, search_term)

    {:noreply, socket}
  end

  def handle_event("filter_status", %{"status" => status}, socket) do
    filter_opts = build_filter_opts(status, socket.assigns.organization_filter, socket.assigns.search_term)
    users = UserManagement.list_users(filter_opts)
    
    socket = 
      socket
      |> assign(:users, users)
      |> assign(:status_filter, status)

    {:noreply, socket}
  end

  def handle_event("change_user_status", params, socket) do
    # Handle both old format and new format
    {user_id, new_status} = case params do
      %{"user_id" => uid, "status" => status} -> {uid, status}
      %{"user_id" => uid, "value" => status} -> {uid, status}
      %{"value" => status} = p when is_map_key(p, "user_id") -> {p["user_id"], status}
      _ ->
        IO.inspect(params, label: "DEBUG: Unexpected params format")
        {nil, nil}
    end

    if user_id && new_status do
      # Store the pending status change without saving to database
      # Ensure user_id is stored as string for consistency with template
      user_id_str = to_string(user_id)
      pending_changes = Map.put(socket.assigns.pending_status_changes, user_id_str, new_status)

      IO.inspect(pending_changes, label: "DEBUG: pending_changes after update")

      socket = assign(socket, :pending_status_changes, pending_changes)
      {:noreply, socket}
    else
      IO.inspect(params, label: "DEBUG: Could not extract user_id and status from params")
      {:noreply, socket}
    end
  end

  def handle_event("save_user_status", %{"user_id" => user_id}, socket) do
    user_id_str = to_string(user_id)
    case Map.get(socket.assigns.pending_status_changes, user_id_str) do
      nil ->
        socket = put_flash(socket, :error, "No status change to save")
        {:noreply, socket}

      new_status ->
        user = UserManagement.get_user!(user_id)
        handle_user_status_update(user, new_status, socket, user_id_str)
    end
  end

  def handle_event("cancel_status_change", %{"user_id" => user_id}, socket) do
    # Remove the pending status change
    user_id_str = to_string(user_id)
    pending_changes = Map.delete(socket.assigns.pending_status_changes, user_id_str)
    socket = assign(socket, :pending_status_changes, pending_changes)
    {:noreply, socket}
  end

  defp handle_user_status_update(user, new_status, socket, user_id) do
    case UserManagement.update_user_status(user, new_status, socket.assigns.current_user.id) do
      {:ok, _updated_user} ->
        # Log the activity
        ActivityLogger.log_system_activity(socket.assigns.current_user.id, "user_status_change",
          resource_type: "user",
          resource_id: user.id,
          metadata: %{old_status: user.status, new_status: new_status}
        )

        # Remove the pending change
        pending_changes = Map.delete(socket.assigns.pending_status_changes, user_id)

        # Refresh the user list with current filters
        filter_opts = build_filter_opts(socket.assigns.status_filter, socket.assigns.organization_filter, socket.assigns.search_term)
        users = UserManagement.list_users(filter_opts)

        socket =
          socket
          |> assign(:users, users)
          |> assign(:pending_status_changes, pending_changes)
          |> put_flash(:info, "User status updated successfully")

        {:noreply, socket}

      {:error, _changeset} ->
        socket = put_flash(socket, :error, "Failed to update user status")
        {:noreply, socket}
    end
  end

  def handle_event("save_user", %{"user" => user_params}, socket) do
    case socket.assigns[:form_mode] do
      :edit ->
        # Update existing user
        case UserManagement.update_user_admin(socket.assigns.user, user_params, socket.assigns.current_user.id) do
          {:ok, _user} ->
            # Refresh the user list with current filters
            filter_opts = build_filter_opts(socket.assigns.status_filter, socket.assigns.organization_filter, socket.assigns.search_term)
            users = UserManagement.list_users(filter_opts)

            socket =
              socket
              |> assign(:users, users)
              |> assign(:show_form, false)
              |> put_flash(:info, "User updated successfully")
              |> push_navigate(to: ~p"/admin/users")

            {:noreply, socket}

          {:error, changeset} ->
            socket =
              socket
              |> assign(:user_form, to_form(changeset))
              |> put_flash(:error, "Failed to update user")

            {:noreply, socket}
        end

      _ ->
        # Create new user
        case UserManagement.create_user(user_params, socket.assigns.current_user.id) do
          {:ok, _user} ->
            # Refresh the user list with current filters
            filter_opts = build_filter_opts(socket.assigns.status_filter, socket.assigns.organization_filter, socket.assigns.search_term)
            users = UserManagement.list_users(filter_opts)

            socket =
              socket
              |> assign(:users, users)
              |> assign(:show_form, false)
              |> put_flash(:info, "User created successfully")
              |> push_navigate(to: ~p"/admin/users")

            {:noreply, socket}

          {:error, changeset} ->
            socket =
              socket
              |> assign(:user_form, to_form(changeset))
              |> assign(:show_form, true)  # Keep the form open
              |> put_flash(:error, "Failed to create user. Please check the errors below.")

            {:noreply, socket}
        end
    end
  end

  def handle_event("cancel_form", _params, socket) do
    socket =
      socket
      |> assign(:show_form, false)
      |> assign(:form_mode, nil)
      |> push_navigate(to: ~p"/admin/users")

    {:noreply, socket}
  end

  def handle_event("validate_user", %{"user" => user_params}, socket) do
    changeset =
      UserManagement.change_user(user_params)
      |> Map.put(:action, :validate)

    socket = assign(socket, :user_form, to_form(changeset))
    {:noreply, socket}
  end

  def handle_event("stop_propagation", _params, socket) do
    # This event handler prevents the modal from closing when clicking inside it
    {:noreply, socket}
  end



  @impl true
  def render(assigns) do
    ~H"""
    <!-- User Form Modal -->
    <%= if Map.get(assigns, :show_form, false) and Map.get(assigns, :form_mode) != :show do %>
      <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" phx-click="cancel_form">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white" phx-click="stop_propagation">
          <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-medium text-gray-900">
                <%= if assigns[:form_mode] == :edit, do: "Edit User", else: "Create New User" %>
              </h3>
              <button phx-click="cancel_form" class="text-gray-400 hover:text-gray-600">
                <.icon name="hero-x-mark" class="w-6 h-6" />
              </button>
            </div>

            <.form for={@user_form} phx-submit="save_user" phx-change="validate_user" class="space-y-4">
              <!-- Error Display -->
              <%= if @user_form.errors != [] do %>
                <div class="bg-red-50 border border-red-200 rounded-md p-3">
                  <div class="flex">
                    <.icon name="hero-exclamation-triangle" class="w-5 h-5 text-red-400 mr-2 mt-0.5" />
                    <div class="text-sm text-red-700">
                      <strong>Please fix the following errors:</strong>
                      <ul class="mt-1 list-disc list-inside">
                        <%= for {field, {message, _}} <- @user_form.errors do %>
                          <li><%= Phoenix.Naming.humanize(field) %>: <%= message %></li>
                        <% end %>
                      </ul>
                    </div>
                  </div>
                </div>
              <% end %>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Email *</label>
                  <.input field={@user_form[:email]} type="email" required class="w-full" />
                </div>

                <%= if assigns[:form_mode] == :edit do %>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">New Password (leave blank to keep current)</label>
                    <.input field={@user_form[:password]} type="password" class="w-full" />
                  </div>
                <% end %>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">First Name</label>
                  <.input field={@user_form[:first_name]} type="text" class="w-full" />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Last Name</label>
                  <.input field={@user_form[:last_name]} type="text" class="w-full" />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Job Title</label>
                  <.input field={@user_form[:job_title]} type="text" class="w-full" />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">Department</label>
                  <.input field={@user_form[:department]} type="text" class="w-full" />
                </div>

                <%= if length(@organizations) > 0 do %>
                  <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Organization</label>
                    <.input field={@user_form[:organization_id]} type="select" options={[{"Select Organization", ""} | Enum.map(@organizations, &{&1.name, &1.id})]} class="w-full" />
                  </div>
                <% end %>
              </div>

              <div class="flex justify-end space-x-3 pt-4 border-t">
                <button type="button" phx-click="cancel_form" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200">
                  Cancel
                </button>
                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700">
                  <%= if assigns[:form_mode] == :edit, do: "Update User", else: "Create User" %>
                </button>
              </div>
            </.form>
          </div>
        </div>
      </div>
    <% end %>

    <!-- User Details View -->
    <%= if Map.get(assigns, :show_form, false) and Map.get(assigns, :form_mode) == :show do %>
      <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" phx-click="cancel_form">
        <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white" phx-click="stop_propagation">
          <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-medium text-gray-900">User Details</h3>
              <button phx-click="cancel_form" class="text-gray-400 hover:text-gray-600">
                <.icon name="hero-x-mark" class="w-6 h-6" />
              </button>
            </div>

            <div class="space-y-6">
              <!-- User Avatar and Basic Info -->
              <div class="flex items-center space-x-4">
                <div class="h-16 w-16 rounded-full bg-gray-300 flex items-center justify-center">
                  <span class="text-xl font-medium text-gray-700">
                    <%= get_user_initials(@user) %>
                  </span>
                </div>
                <div>
                  <h4 class="text-xl font-semibold text-gray-900"><%= get_user_display_name(@user) %></h4>
                  <p class="text-gray-600"><%= @user.email %></p>
                  <span class={status_badge_class(@user.status)}>
                    <%= String.capitalize(@user.status || "active") %>
                  </span>
                </div>
              </div>

              <!-- User Information Grid -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h5 class="text-sm font-medium text-gray-700 mb-2">Personal Information</h5>
                  <div class="space-y-2">
                    <%= if @user.profile do %>
                      <div>
                        <span class="text-sm text-gray-500">First Name:</span>
                        <span class="text-sm text-gray-900 ml-2"><%= @user.profile.first_name || "Not set" %></span>
                      </div>
                      <div>
                        <span class="text-sm text-gray-500">Last Name:</span>
                        <span class="text-sm text-gray-900 ml-2"><%= @user.profile.last_name || "Not set" %></span>
                      </div>
                      <div>
                        <span class="text-sm text-gray-500">Phone:</span>
                        <span class="text-sm text-gray-900 ml-2"><%= @user.profile.phone_number || "Not set" %></span>
                      </div>
                    <% else %>
                      <p class="text-sm text-gray-500">No profile information available</p>
                    <% end %>
                  </div>
                </div>

                <div>
                  <h5 class="text-sm font-medium text-gray-700 mb-2">Work Information</h5>
                  <div class="space-y-2">
                    <%= if @user.profile do %>
                      <div>
                        <span class="text-sm text-gray-500">Job Title:</span>
                        <span class="text-sm text-gray-900 ml-2"><%= @user.profile.job_title || "Not set" %></span>
                      </div>
                      <div>
                        <span class="text-sm text-gray-500">Department:</span>
                        <span class="text-sm text-gray-900 ml-2"><%= @user.profile.department || "Not set" %></span>
                      </div>
                    <% else %>
                      <p class="text-sm text-gray-500">No work information available</p>
                    <% end %>
                    <div>
                      <span class="text-sm text-gray-500">Organization:</span>
                      <span class="text-sm text-gray-900 ml-2">
                        <%= if @user.organization_id do %>
                          <%= get_organization_name(@user.organization_id) %>
                        <% else %>
                          Not assigned
                        <% end %>
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Roles and Permissions -->
              <div>
                <h5 class="text-sm font-medium text-gray-700 mb-2">Roles & Permissions</h5>
                <div class="flex flex-wrap gap-2">
                  <%= for role <- get_user_roles(@user) do %>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                      <%= role.name %>
                    </span>
                  <% end %>
                </div>
              </div>

              <!-- Account Information -->
              <div>
                <h5 class="text-sm font-medium text-gray-700 mb-2">Account Information</h5>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span class="text-sm text-gray-500">Last Login:</span>
                    <span class="text-sm text-gray-900 ml-2">
                      <%= if @user.last_login_at do %>
                        <%= Calendar.strftime(@user.last_login_at, "%Y-%m-%d %H:%M") %>
                      <% else %>
                        Never
                      <% end %>
                    </span>
                  </div>
                  <div>
                    <span class="text-sm text-gray-500">Account Created:</span>
                    <span class="text-sm text-gray-900 ml-2">
                      <%= Calendar.strftime(@user.inserted_at, "%Y-%m-%d %H:%M") %>
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div class="flex justify-end space-x-3 pt-6 border-t mt-6">
              <button type="button" phx-click="cancel_form" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200">
                Close
              </button>
              <%= if can_edit_users?(@current_user) do %>
                <.link navigate={~p"/admin/users/#{@user.id}/edit"} class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700">
                  Edit User
                </.link>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    <% end %>

    <div class="space-y-6">
      <!-- Header -->
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">User Management</h1>
          <p class="text-gray-600">Manage users, roles, and permissions</p>
        </div>
        
        <%= if can_create_users?(@current_user) do %>
          <.link navigate={~p"/admin/users/new"} class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            <.icon name="hero-plus" class="w-4 h-4 mr-2" />
            Add User
          </.link>
        <% end %>
      </div>

      <!-- Search and Filters -->
      <div class="bg-white p-4 rounded-lg shadow-sm border">
        <div class="flex flex-col sm:flex-row gap-4">
          <!-- Search -->
          <div class="flex-1">
            <.form for={%{}} phx-submit="search" class="flex">
              <input 
                type="text" 
                name="search" 
                value={@search_term}
                placeholder="Search users by email or name..."
                class="flex-1 px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 transition-colors">
                <.icon name="hero-magnifying-glass" class="w-4 h-4" />
              </button>
            </.form>
          </div>

          <!-- Status Filter -->
          <div>
            <select phx-change="filter_status" name="status" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
              <option value="all" selected={@status_filter == "all"}>All Status</option>
              <option value="active" selected={@status_filter == "active"}>Active</option>
              <option value="inactive" selected={@status_filter == "inactive"}>Inactive</option>
              <option value="suspended" selected={@status_filter == "suspended"}>Suspended</option>
              <option value="pending" selected={@status_filter == "pending"}>Pending</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Users Table -->
      <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Roles</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <%= for user <- @users do %>
                <tr class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                          <span class="text-sm font-medium text-gray-700">
                            <%= get_user_initials(user) %>
                          </span>
                        </div>
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900"><%= user.email %></div>
                        <div class="text-sm text-gray-500"><%= get_user_display_name(user) %></div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class={status_badge_class(user.status)}>
                      <%= String.capitalize(user.status || "active") %>
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex flex-wrap gap-1">
                      <%= for role <- get_user_roles(user) do %>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          <%= role.name %>
                        </span>
                      <% end %>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <%= if user.last_login_at do %>
                      <%= Calendar.strftime(user.last_login_at, "%Y-%m-%d %H:%M") %>
                    <% else %>
                      Never
                    <% end %>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                      <.link navigate={~p"/admin/users/#{user.id}"} class="text-blue-600 hover:text-blue-900">
                        View
                      </.link>
                      <%= if can_edit_users?(@current_user) do %>
                        <.link navigate={~p"/admin/users/#{user.id}/edit"} class="text-indigo-600 hover:text-indigo-900">
                          Edit
                        </.link>
                      <% end %>
                      <%= if can_manage_user_status?(@current_user, user) do %>
                        <div class="flex items-center space-x-2">
                          <form>
                            <input type="hidden" name="user_id" value={user.id} />
                            <select name="status" class="text-sm border-gray-300 rounded" phx-change="change_user_status" phx-value-user_id={user.id}>
                              <%
                                current_status = Map.get(@pending_status_changes, to_string(user.id), user.status)
                              %>
                              <option value="active" selected={current_status == "active"}>Active</option>
                              <option value="inactive" selected={current_status == "inactive"}>Inactive</option>
                              <option value="suspended" selected={current_status == "suspended"}>Suspended</option>
                            </select>
                          </form>

                          <%
                            user_id_str = to_string(user.id)
                            has_pending_change = Map.has_key?(@pending_status_changes, user_id_str)
                          %>

                          <%= if has_pending_change do %>
                            <button
                              phx-click="save_user_status"
                              phx-value-user_id={user.id}
                              class="px-2 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700"
                            >
                              Save
                            </button>
                            <button
                              phx-click="cancel_status_change"
                              phx-value-user_id={user.id}
                              class="px-2 py-1 text-xs bg-gray-400 text-white rounded hover:bg-gray-500"
                            >
                              Cancel
                            </button>
                          <% else %>
                            <span class="text-xs text-gray-500">Change status to see save button</span>
                          <% end %>
                        </div>
                      <% end %>
                    </div>
                  </td>
                </tr>
              <% end %>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Stats -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-white p-4 rounded-lg shadow-sm border">
          <div class="text-2xl font-bold text-gray-900"><%= length(@users) %></div>
          <div class="text-sm text-gray-500">Total Users</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow-sm border">
          <div class="text-2xl font-bold text-green-600"><%= count_users_by_status(@users, "active") %></div>
          <div class="text-sm text-gray-500">Active Users</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow-sm border">
          <div class="text-2xl font-bold text-yellow-600"><%= count_users_by_status(@users, "inactive") %></div>
          <div class="text-sm text-gray-500">Inactive Users</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow-sm border">
          <div class="text-2xl font-bold text-red-600"><%= count_users_by_status(@users, "suspended") %></div>
          <div class="text-sm text-gray-500">Suspended Users</div>
        </div>
      </div>
    </div>
    """
  end

  # Helper functions
  defp can_manage_users?(user) do
    UserManagement.user_has_role?(user, "admin") || 
    UserManagement.user_has_role?(user, "manager") ||
    UserManagement.user_has_permission?(user, "users", "read")
  end

  defp can_create_users?(user) do
    UserManagement.user_has_role?(user, "admin") || 
    UserManagement.user_has_permission?(user, "users", "create")
  end

  defp can_edit_users?(user) do
    UserManagement.user_has_role?(user, "admin") || 
    UserManagement.user_has_permission?(user, "users", "update")
  end

  defp can_manage_user_status?(current_user, target_user) do
    UserManagement.user_has_role?(current_user, "admin") ||
    (UserManagement.user_has_role?(current_user, "manager") && current_user.id != target_user.id)
  end

  defp get_user_display_name(user) do
    case user.profile do
      %{first_name: first, last_name: last} when not is_nil(first) and not is_nil(last) ->
        "#{first} #{last}"
      _ ->
        "No name set"
    end
  end

  defp get_user_initials(user) do
    case user.profile do
      %{first_name: first, last_name: last} when not is_nil(first) and not is_nil(last) ->
        "#{String.first(first)}#{String.first(last)}" |> String.upcase()
      _ ->
        user.email |> String.first() |> String.upcase()
    end
  end

  defp get_user_roles(user) do
    case user.role_assignments do
      assignments when is_list(assignments) ->
        Enum.map(assignments, & &1.role)
      _ ->
        []
    end
  end

  defp status_badge_class("active"), do: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
  defp status_badge_class("inactive"), do: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
  defp status_badge_class("suspended"), do: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800"
  defp status_badge_class("pending"), do: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
  defp status_badge_class(_), do: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"

  defp count_users_by_status(users, status) do
    Enum.count(users, &(&1.status == status))
  end

  defp build_filter_opts(status, organization, search) do
    []
    |> maybe_add_status_filter(status)
    |> maybe_add_organization_filter(organization)
    |> maybe_add_search_filter(search)
  end

  defp maybe_add_status_filter(opts, "all"), do: opts
  defp maybe_add_status_filter(opts, status), do: Keyword.put(opts, :status, status)

  defp maybe_add_organization_filter(opts, "all"), do: opts
  defp maybe_add_organization_filter(opts, org_id), do: Keyword.put(opts, :organization_id, org_id)

  defp maybe_add_search_filter(opts, ""), do: opts
  defp maybe_add_search_filter(opts, search), do: Keyword.put(opts, :search, search)

  defp get_organization_name(organization_id) do
    case UserManagement.get_organization(organization_id) do
      nil -> "Unknown Organization"
      org -> org.name
    end
  end
end
